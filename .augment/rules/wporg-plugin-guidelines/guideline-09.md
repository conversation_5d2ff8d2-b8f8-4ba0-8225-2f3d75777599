<h4>9. Developers and their plugins must not do anything illegal, dishonest, or morally offensive.</h4>

While this is subjective and rather broad, the intent is to prevent plugins, developers, and companies from abusing the freedoms and rights of end users as well as other plugin developers.

This includes (but is not restricted to) the following examples:
<ul>
	<li>Artificially manipulating search results via keyword stuffing, black hat SEO, or otherwise</li>
	<li>Offering to drive more traffic to sites that use the plugin</li>
	<li>Compensating, misleading, pressuring, extorting, or blackmailing others for reviews or support</li>
	<li>Implying users must pay to unlock included features</li>
	<li>Creating accounts to generate fake reviews or support tickets (i.e. sockpuppeting)</li>
	<li>Taking other developers’ plugins and presenting them as original work</li>
	<li>Implying that a plugin can create, provide, or guarantee legal compliance</li>
	<li>Utilizing the user’s server or resources without permission, such as part of a botnet or crypto-mining</li>
	<li>Violations of the <a href="https://make.wordpress.org/handbook/community-code-of-conduct/">WordPress.org Community Code of Conduct</a>
	<li>Violations of the <a href="https://make.wordpress.org/community/handbook/wordcamp-organizer/planning-details/code-of-conduct/">WordCamp code of conduct<a></li>
	<li>Violations of the <a href="https://wordpress.org/support/guidelines/">Forum Guidelines</a></li>
	<li>Harassment, threats, or abuse directed at any other member of the WordPress community</li>
	<li>Falsifying personal information to intentionally disguise identities and avoid sanctions for previous infractions</li>
	<li>Intentionally attempting to exploit loopholes in the guidelines</li>
</ul>
