<h4>15. Plugin version numbers must be incremented for each new release.</h4>

Users are only alerted to updates when the plugin version is increased. The trunk readme.txt must always reflect the current version of the plugin. For more information on tagging, please read our <a href="https://developer.wordpress.org/plugins/wordpress-org/how-to-use-subversion/#tags">SVN directions on tagging</a> and <a href="https://developer.wordpress.org/plugins/wordpress-org/how-your-readme-txt-works/">how the readme.txt works</a>.
