// Review Kit common styles
@use "variables";

.reviewkit_fpln_common {
    display: inline-flex;
    gap: 5px;
    padding: 22px 26px;
    border-radius: 4px;
    border: 1px solid variables.$primary-color;
    text-decoration: none;
    font-size: 16px;
    font-weight: 600;
    color: variables.$body-text-color;

}

.reviewkit_fpln_star_icon {
    width: 16px;
    height: 16px;
    margin-right: 4px;
    fill: variables.$primary-color;
}


/* Star Rating Container */
.reviewkit_star_rating {
    display: inline-flex;
    flex-direction: row-reverse;
    justify-content: start;
    gap: 5px;
    margin-bottom: 15px;
}

/* Hide radio buttons */
.reviewkit_star_rating input {
    display: none;
}

/* Style star labels */
.reviewkit_star_rating label {
    font-size: 40px;
    line-height: 1;
    color: #ddd;
    cursor: pointer;
    transition: all 0.3s ease;
    user-select: none;
}

/* Hover effect */
.reviewkit_star_rating label:hover {
    transform: scale(1.1);
}

/* Default hover color (yellow) */
.reviewkit_star_rating label:hover,
.reviewkit_star_rating label:hover~label {
    color: #ffce00;
}

/* 1 Star Selected - RED */
.reviewkit_star_rating input[value="1"]:checked~label[for="star1"] {
    color: #ff3722;
}

/* 2 Stars Selected - ORANGE */
.reviewkit_star_rating input[value="2"]:checked~label[for="star2"],
.reviewkit_star_rating input[value="2"]:checked~label[for="star1"] {
    color: #ff8622;
}

/* 3 Stars Selected - YELLOW */
.reviewkit_star_rating input[value="3"]:checked~label[for="star3"],
.reviewkit_star_rating input[value="3"]:checked~label[for="star2"],
.reviewkit_star_rating input[value="3"]:checked~label[for="star1"] {
    color: #ffce00;
}

/* 4 Stars Selected - BLUE */
.reviewkit_star_rating input[value="4"]:checked~label[for="star4"],
.reviewkit_star_rating input[value="4"]:checked~label[for="star3"],
.reviewkit_star_rating input[value="4"]:checked~label[for="star2"],
.reviewkit_star_rating input[value="4"]:checked~label[for="star1"] {
    color: #73cf11;
}

/* 5 Stars Selected - GREEN */
.reviewkit_star_rating input[value="5"]:checked~label[for="star5"],
.reviewkit_star_rating input[value="5"]:checked~label[for="star4"],
.reviewkit_star_rating input[value="5"]:checked~label[for="star3"],
.reviewkit_star_rating input[value="5"]:checked~label[for="star2"],
.reviewkit_star_rating input[value="5"]:checked~label[for="star1"] {
    color: #00b67a;
}

/* Override hover when rating is selected */
.reviewkit_star_rating input[value="1"]:checked~label:hover,
.reviewkit_star_rating input[value="1"]:checked~label:hover~label {
    color: #ff3722;
}

.reviewkit_star_rating input[value="2"]:checked~label:hover,
.reviewkit_star_rating input[value="2"]:checked~label:hover~label {
    color: #ff8622;
}

.reviewkit_star_rating input[value="3"]:checked~label:hover,
.reviewkit_star_rating input[value="3"]:checked~label:hover~label {
    color: #ffce00;
}

.reviewkit_star_rating input[value="4"]:checked~label:hover,
.reviewkit_star_rating input[value="4"]:checked~label:hover~label {
    color: #73cf11;
}

.reviewkit_star_rating input[value="5"]:checked~label:hover,
.reviewkit_star_rating input[value="5"]:checked~label:hover~label {
    color: #00b67a;
}