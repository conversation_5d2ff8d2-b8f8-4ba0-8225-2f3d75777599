<?php

if ( ! defined( 'ABSPATH' ) ) {
	exit; // Exit if accessed directly
}

/**
 * Business Data Manager
 * Handles storage and retrieval of business profile data from wp_options
 */
class Reviewkit_Business_Data {

	/**
	 * Option key for storing business data
	 */
	const OPTION_KEY = 'reviewkit_tp_reviews_business_data'; // reviewkit_tp_reviews_data

	/**
	 * Default business data structure
	 */
	private static $default_data = array(
		'business_name'     => 'Your Business',
		'trust_score'       => 4.8,
		'review_count'      => 347,
		'website_url'       => '#',
		'business_category' => 'Technology',
		'is_verified'       => true,
		'is_claimed'        => true,
		'last_updated'      => '',
		// API response keys for compatibility
		'identifyingName'   => 'example.com',
		'trustScore'        => 4.8,
		'numberOfReviews'   => 347,
		'websiteUrl'        => '#',
		'displayName'       => 'Your Business',
	);

	public $profile = array();


	public function __construct() {
		$this->profile = self::get_business_data();
	}

	/**
	 * Get business data from wp_options
	 *
	 * @return array Business data
	 */
	public static function get_business_data() {
		$data = get_option( self::OPTION_KEY, self::$default_data );

		// Ensure all required keys exist
		$data = wp_parse_args( $data, self::$default_data );

		// Ensure trust_score is within valid range
		$data['trust_score'] = max( 1.0, min( 5.0, floatval( $data['trust_score'] ) ) );

		// Ensure review_count is positive integer
		$data['review_count'] = max( 0, intval( $data['review_count'] ) );

		return $data;
	}

	/**
	 * Update business data in wp_options
	 *
	 * @param array $data Business data to update
	 * @return bool Success status
	 */
	public static function update_business_data( $data ) {
		$current_data = self::get_business_data();
		$updated_data = wp_parse_args( $data, $current_data );

		// Add timestamp
		$updated_data['last_updated'] = current_time( 'mysql' );

		// Validate and sanitize data
		$updated_data = self::sanitize_business_data( $updated_data );

		return update_option( self::OPTION_KEY, $updated_data );
	}

	/**
	 * Sanitize business data
	 *
	 * @param array $data Raw business data
	 * @return array Sanitized business data
	 */
	private static function sanitize_business_data( $data ) {
		return array(
			'business_name'     => sanitize_text_field( $data['business_name'] ?? '' ),
			'trust_score'       => max( 1.0, min( 5.0, floatval( $data['trust_score'] ?? 4.8 ) ) ),
			'review_count'      => max( 0, intval( $data['review_count'] ?? 0 ) ),
			'website_url'       => esc_url_raw( $data['website_url'] ?? '#' ),
			'business_category' => sanitize_text_field( $data['business_category'] ?? '' ),
			'is_verified'       => (bool) ( $data['is_verified'] ?? false ),
			'is_claimed'        => (bool) ( $data['is_claimed'] ?? false ),
			'last_updated'      => sanitize_text_field( $data['last_updated'] ?? '' ),
		);
	}

	/**
	 * Get formatted review count for display
	 *
	 * @param int $count Review count
	 * @return string Formatted count (e.g., "1.2K", "347")
	 */
	public static function format_review_count( $count = null ) {
		if ( $count === null ) {
			$data  = self::get_business_data();
			$count = $data['numberOfReviews'] ?? $data['review_count'] ?? 347;
		}
		$count = (int) $count;

		if ( $count >= 1000000 ) {
			return number_format( $count / 1000000, 1 ) . 'M';
		} elseif ( $count >= 1000 ) {
			return number_format( $count / 1000, 1 ) . 'K';
		}

		return number_format( $count );
	}

	/**
	 * Get trust score rounded to nearest 0.5 for image selection
	 *
	 * @param float $score Trust score
	 * @return float Rounded score
	 */
	public static function get_rounded_trust_score( $score = null ) {
		if ( $score === null ) {
			$data  = self::get_business_data();
			$score = $data['trust_score'];
		}

		return round( $score * 2 ) / 2;
	}

	/**
	 * Get star rating image filename based on trust score
	 *
	 * @param float $score Trust score
	 * @return string Image filename
	 */
	public static function get_star_image_filename( $score = null ) {
		$rounded_score = self::get_rounded_trust_score( $score );

		// Convert to string format for filename
		// $filename = str_replace( '.', '_', number_format( $rounded_score, 1 ) );
		$filename = number_format( $rounded_score, 1 );

		return "stars-{$filename}.svg";
	}

	/**
	 * Get star rating image URL
	 *
	 * @param float $score Trust score
	 * @return string Image URL
	 */
	public static function get_star_image_url( $score = null ) {
		$filename = self::get_star_image_filename( $score );
		return plugin_dir_url( __DIR__ ) . "assets/images/{$filename}";
	}

	/**
	 * Get single star image URL
	 *
	 * @param float $score Trust score
	 * @return string Image URL
	 */
	public static function get_single_star_image_url() {
		return plugin_dir_url( __DIR__ ) . 'assets/images/single-star.svg';
	}

	/**
	 * Get rating text based on trust score
	 *
	 * @param float $score Trust score
	 * @return string Rating text
	 */
	public static function get_rating_text( $score = null ) {
		if ( $score === null ) {
			$data  = self::get_business_data();
			$score = $data['trustScore'] ?? $data['trust_score'] ?? 4.8;
		}
		if ( $score >= 4.5 ) {
			return 'Excellent';
		} elseif ( $score >= 4.0 ) {
			return 'Great';
		} elseif ( $score >= 3.0 ) {
			return 'Good';
		} elseif ( $score >= 2.0 ) {
			return 'Average';
		} else {
			return 'Poor';
		}
	}

	/**
	 * Initialize default data if not exists
	 */
	public static function init_default_data() {
		if ( ! get_option( self::OPTION_KEY ) ) {
			// Try to get business details from the separated business data storage
			$business_details = get_option( 'reviewkit_tp_reviews_business_data', null );

			// If no business details found, try to get from old format (for backward compatibility)
			if ( ! $business_details ) {
				$review_data = get_option( 'reviewkit_tp_reviews_data', null );
				if ( $review_data && isset( $review_data['business_details'] ) ) {
					$business_details = $review_data['business_details'];
				}
			}

			if ( $business_details ) {
				// Get reviews count from the separated reviews storage
				$reviews      = get_option( 'reviewkit_tp_reviews_data', array() );
				$review_count = is_array( $reviews ) ? count( $reviews ) : 0;

				$migrated_data = array(
					'business_name'     => $business_details['displayName'] ?? self::$default_data['business_name'],
					'trust_score'       => floatval( $business_details['trustScore'] ?? self::$default_data['trust_score'] ),
					'review_count'      => intval( $business_details['numberOfReviews'] ?? $review_count ),
					'website_url'       => $business_details['websiteUrl'] ?? self::$default_data['website_url'],
					'business_category' => $business_details['categoryName'] ?? self::$default_data['business_category'],
					'is_verified'       => (bool) ( $business_details['isVerified'] ?? self::$default_data['is_verified'] ),
					'is_claimed'        => (bool) ( $business_details['isClaimed'] ?? self::$default_data['is_claimed'] ),
					'last_updated'      => current_time( 'mysql' ),
				);

				update_option( self::OPTION_KEY, $migrated_data );
			} else {
				// No existing data, use defaults
				update_option( self::OPTION_KEY, self::$default_data );
			}
		}
	}

	/**
	 * Migrate data from old domain-specific format to new simplified format
	 * This function helps transition from the old multi-domain system
	 */
	public static function migrate_from_domain_specific() {
		global $wpdb;

		// Find any existing domain-specific data
		$query   = "SELECT option_name, option_value FROM {$wpdb->options} WHERE option_name LIKE 'reviewkit_tp_reviews_data_%'";
		$results = $wpdb->get_results( $wpdb->prepare( $query ), ARRAY_A ); // phpcs:ignore

		if ( ! empty( $results ) ) {
			// Take the first (most recent) domain data found
			$domain_data = maybe_unserialize( $results[0]['option_value'] );

			if ( $domain_data && isset( $domain_data['business_details'] ) ) {
				// Migrate to new simplified format
				update_option( 'reviewkit_tp_reviews_data', $domain_data );

				// Also migrate other related options
				$domain_suffix = str_replace( 'reviewkit_tp_reviews_data_', '', $results[0]['option_name'] );

				$original_domain = get_option( 'reviewkit_tp_reviews_original_domain_' . $domain_suffix );
				$domain          = get_option( 'reviewkit_tp_reviews_domain_' . $domain_suffix );
				$count           = get_option( 'reviewkit_tp_reviews_count_' . $domain_suffix );
				$last_updated    = get_option( 'reviewkit_tp_reviews_last_updated_' . $domain_suffix );

				if ( $original_domain ) {
					update_option( 'reviewkit_tp_reviews_original_domain', $original_domain );
				}
				if ( $domain ) {
					update_option( 'reviewkit_tp_reviews_domain', $domain );
				}
				if ( $count ) {
					update_option( 'reviewkit_tp_reviews_count', $count );
				}
				if ( $last_updated ) {
					update_option( 'reviewkit_tp_reviews_last_updated', $last_updated );
				}

				// Initialize business data from migrated review data
				self::init_default_data();

				// Clean up old domain-specific data
				self::cleanup_old_domain_data();
			}
		}
	}

	/**
	 * Clean up old domain-specific data
	 */
	private static function cleanup_old_domain_data() {
		global $wpdb;

		// Delete all old domain-specific options
		$wpdb->query( "DELETE FROM {$wpdb->options} WHERE option_name LIKE 'reviewkit_tp_reviews_%_%'" ); // phpcs:ignore
	}
}
