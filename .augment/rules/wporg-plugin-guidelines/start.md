I need you to perform a comprehensive WordPress.org plugin directory compliance audit for the BlockBucket plugin. Here are the specific steps:

1. **Read and analyze all WordPress.org plugin guidelines** from the `/Users/<USER>/Documents/sites/project/blocks/wp-content/plugins/blockbucket/wporg-plugin-guidelines/` directory to understand the complete set of requirements for plugin submission.

2. **Scan the entire BlockBucket plugin codebase** (located at `/Users/<USER>/Documents/sites/project/blocks/wp-content/plugins/blockbucket/`) and systematically check each file against the WordPress.org guidelines.

3. **Identify specific guideline violations** by:
   - Checking code quality and security standards
   - Verifying proper WordPress coding standards compliance
   - Ensuring no prohibited functionality (like phone-home features, external dependencies, etc.)
   - Validating plugin header information and metadata
   - Checking for proper sanitization, validation, and escaping
   - Verifying GPL compatibility and licensing requirements
   - Ensuring no trademark or naming violations

4. **Generate a detailed compliance report** that includes:
   - A list of all identified guideline violations
   - Specific file locations and line numbers where violations occur
   - Clear descriptions of what needs to be fixed
   - References to the specific WordPress.org guidelines that are being violated

5. **Create a structured task list** for fixing all identified issues, organized by priority and complexity, but do NOT execute any of these tasks without my explicit approval.

The goal is to prepare the BlockBucket plugin for successful submission to the WordPress.org plugin directory by ensuring 100% compliance with all guidelines.