<h2>The Plugin Directory</h2>

The goal of the WordPress Plugin Directory is to provide a safe place for all WordPress users - from the non-technical to the developer - to download plugins that are consistent with the goals of the WordPress project.

To that end, we want to ensure a simple and transparent process for developers to submit plugins for the directory. As part of our ongoing efforts to make the plugin directory inclusion process more transparent, we have created a list of developer guidelines. We strive to create a level playing field for all developers.

If you have suggestions to improve the guidelines, or questions about them, please email <code><EMAIL></code> and let us know.

<h2>Developer Expectations</h2>

Developers, all users with commit access, and all users who officially support a plugin are expected to abide by the following guidelines:

* Plugin Directory Guidelines (this document)
* <a href="https://make.wordpress.org/handbook/community-code-of-conduct/">Community Code of Conduct</a>
* <a href="https://wordpress.org/support/guidelines/">Forums Guidelines</a> (should they use the forums/reviews)

Violations may result in plugins or plugin data (for previously approved plugins) being removed from the directory until the issues are resolved. Plugin data, such as user reviews and code, may not be restored depending on the nature of the violation and the results of a peer-review of the situation. Repeat violations may result in all the author’s plugins being removed and the developer being banned from hosting plugins on WordPress.org.

It is the responsibility of the plugin developer to ensure their contact information on WordPress.org is up to date and accurate, in order that they receive all notifications from the plugins team. Auto-replies and emails that route to a support system are not permitted as they historically prevent humans from addressing emails in a timely fashion.

All code in the directory should be made as secure as possible. Security is the ultimate responsibility of the plugin developer, and the Plugin Directory enforces this to the best of our ability. Should a plugin be found to have security issues, it will be closed until the situation is resolved. In extreme cases the plugin may be updated by the WordPress Security team and propagated for the safety of the general public.

While we attempt to account for as many relevant interpretations of the guidelines as possible, it is unreasonable to expect that every circumstance will be explicitly covered. If you are uncertain whether a plugin might violate the guidelines, please contact <code><EMAIL></code> and ask.
