<h4>17. Plugins must respect trademarks, copyrights, and project names.</h4>

The use of trademarks or other projects as the sole or initial term of a plugin slug is prohibited unless proof of legal ownership/representation can be confirmed. For example, the <a href="http://wordpressfoundation.org/trademark-policy/">WordPress Foundation has trademarked the term “WordPress”</a> and it is a violation to use “wordpress” in a domain name. This policy extends to plugin slugs, and we will not permit a slug to begin with another product's term.

For example only employees of Super Sandbox should use the slug “super-sandbox," or their brand in a context such as “Super Sandbox Dancing Sloths.” Non-employees should use a format such as “Dancing Sloths for Superbox” instead to avoid potentially misleading users into believing the plugin was developed by Super Sandbox. Similarly, if you don't represent the "MellowYellowSandbox.js" project, it's inappropriate to use that as the name of your plugin.

Original branding is recommended as it not only helps to avoid confusion, but is more memorable to the user.