<?php

if ( ! defined( 'ABSPATH' ) ) {
	exit; // Exit if accessed directly
}

class Reviewkit_Admin_Ajax {
	public function __construct() {
		add_action( 'wp_ajax_reviewkit_get_reviews', array( $this, 'get_reviews' ) );
	}

	public function get_reviews() {
		check_ajax_referer( 'reviewkit_nonce', 'security' );

		$domain     = isset( $_POST['domain'] ) ? sanitize_text_field( $_POST['domain'] ) : ''; // phpcs:ignore
		$revalidate = isset( $_POST['revalidate'] ) ? sanitize_text_field( wp_unslash( $_POST['revalidate'] ) ) : '';

		if ( empty( $domain ) ) {
			wp_send_json_error( array( 'message' => 'Domain is required' ), 400 );
		}

		$original_domain = $domain;
		$domain          = $this->process_domain( $domain );

		$previous_original_domain = $this->get_previous_domain();

		if ( $original_domain !== $previous_original_domain ) {
			$this->delete_cache();
		}

		if ( ! empty( $revalidate ) ) {
			$cache = $this->get_cache();

			if ( ! empty( $cache ) ) {
				wp_send_json_success( $cache );
			}
		}

		// Prepare the request to the API
		$api_url = REVIEWKIT_TP_API . urlencode( $domain ); // phpcs:ignore

		$args = array(
			'timeout'     => 30,
			'redirection' => 5,
			'httpversion' => '1.1',
			'user-agent'  => 'WordPress/' . get_bloginfo( 'version' ) . '; ' . get_bloginfo( 'url' ),
			'headers'     => array(
				'Accept'       => 'application/json',
				'Content-Type' => 'application/json',
			),
		);

		// Make the request
		$response = wp_remote_get( $api_url, $args );

		if ( is_wp_error( $response ) ) {
			wp_send_json_error(
				array(
					'message' => 'Failed to fetch reviews: ' . $response->get_error_message(),
				),
				500
			);
		}

		$response_code = wp_remote_retrieve_response_code( $response );

		if ( $response_code !== 200 ) {
			wp_send_json_error(
				array(
					'message'  => 'API returned error code: ' . $response_code,
					'response' => wp_remote_retrieve_body( $response ),
				),
				$response_code
			);
		}

		// Get and parse the response body
		$body = wp_remote_retrieve_body( $response );
		$data = json_decode( $body, true );

		if ( json_last_error() !== JSON_ERROR_NONE ) {
			wp_send_json_error(
				array(
					'message' => 'Failed to parse API response: ' . json_last_error_msg(),
				),
				500
			);
		}

		update_option( 'reviewkit_tp_reviews_original_domain', $original_domain );
		$updated_at = $this->processReviews( $domain, $data );
		wp_send_json_success(
			array(
				'data'         => $data,
				'last_updated' => $updated_at,
			)
		);
	}


	public function processReviews( $domain, $data ) {
		if ( empty( $data ) ) {
			return;
		}

		// Extract business details (keep existing storage)
		$business_details = $data['business_details'];
		update_option( 'reviewkit_tp_reviews_business_data', $business_details );

		// Extract reviews array only
		$reviews = isset( $data['reviews'] ) ? $data['reviews'] : array();
		update_option( 'reviewkit_tp_reviews_data', $reviews );

		// Store remaining response data (excluding business_details and reviews)
		$misc_data = $data;
		unset( $misc_data['business_details'] );
		unset( $misc_data['reviews'] );
		update_option( 'reviewkit_tp_reviews_misc_data', $misc_data );

		// Update other related options
		update_option( 'reviewkit_tp_reviews_domain', $domain );
		update_option( 'reviewkit_tp_reviews_count', count( $reviews ) );
		$time = time();
		update_option( 'reviewkit_tp_reviews_last_updated', $time );

		return $time;
	}

	public function process_domain( $domain ) {
		 $parts = wp_parse_url($domain);

		if ($parts === false || !isset($parts['path'])) {
			return false;
		}

		// Trim trailing slash, then split path and grab the last segment
		$path = rtrim($parts['path'], '/');
		$segments = explode('/', $path);

		return array_pop($segments);
	}

	public function get_previous_domain() {
		return get_option( 'reviewkit_tp_reviews_original_domain', '' );
	}

	public function get_cache() {
		if ( empty( get_option( 'reviewkit_tp_reviews_last_updated', null ) ) ) {
			return false;
		}

		// Reconstruct the complete API response from separated data
		$business_details = get_option( 'reviewkit_tp_reviews_business_data', array() );
		$reviews          = get_option( 'reviewkit_tp_reviews_data', array() );
		$misc_data        = get_option( 'reviewkit_tp_reviews_misc_data', array() );

		// Combine all data to maintain API compatibility
		$complete_data                     = $misc_data;
		$complete_data['business_details'] = $business_details;
		$complete_data['reviews']          = $reviews;

		return array(
			'data'         => $complete_data,
			'last_updated' => get_option( 'reviewkit_tp_reviews_last_updated', null ),
		);
	}

	public function delete_cache() {
		delete_option( 'reviewkit_tp_reviews_original_domain' );
		delete_option( 'reviewkit_tp_reviews_domain' );
		delete_option( 'reviewkit_tp_reviews_data' );
		delete_option( 'reviewkit_tp_reviews_misc_data' );
		delete_option( 'reviewkit_tp_reviews_count' );
		delete_option( 'reviewkit_tp_reviews_last_updated' );
		delete_option( 'reviewkit_tp_reviews_business_data' );
	}
}
