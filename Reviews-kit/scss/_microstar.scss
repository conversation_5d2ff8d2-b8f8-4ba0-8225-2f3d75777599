@use "variables";

.reviewkit_fpln_mcs {
    align-items: center;
    flex-wrap: wrap;
    gap: 15px;

    .reviewkit_fpln_mc_inner_left {
        display: flex;
        align-items: center;
        flex-wrap: wrap;
        gap: 15px;

        .review_us_one {
            font-weight: 700;
        }

        .reviewkit_star_rating {
            display: inline-flex;
            margin-bottom: 0;

            label {
                font-size: 26px;
                color: variables.$primary-color;
            }
        }
    }

    .reviewkit_fpln_mc_inner_right {
        .review_us_one {
            font-weight: 400;
        }

        .reviewkit_fpln_star_icon {
            margin-right: 0;
        }
    }

}