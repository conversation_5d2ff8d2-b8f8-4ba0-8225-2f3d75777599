# WordPress Plugin Finalization for WordPress.org Submission

Please finalize this WordPress plugin to meet WordPress.org plugin submission guidelines. Follow these requirements strictly without breaking existing functionality.

## Plugin Metadata Updates
- **Plugin Name**: Reviewkit
- **Author**: GutenSuite  
- **Text Domain**: reviewkit
- **Version**: 1.0.0

## Security and Code Standards

### 1. ABSPATH Security Check
Add this security check to the top of every PHP file (after the opening <?php tag):
```php
if ( ! defined( 'ABSPATH' ) ) {
    exit; // Exit if accessed directly
}
```

### 2. Constants Naming Convention
- Prefix all PHP constants with `REVIEWKIT_`
- Example: Change `TRUSTPILOT_REVIEWSKIT_SLUG` to `REVIEWKIT_SLUG`
- Update all constants throughout the codebase

### 3. File Naming Convention
Rename PHP files to use WordPress naming standards:
- Change `Ajax.php` to `class-ajax.php`
- Change `Assets.php` to `class-assets.php`
- Apply this pattern to all PHP class files

### 4. Class Naming Convention
- Prefix all PHP classes with `Reviewkit_`
- Example: `Ajax` becomes `Reviewkit_Ajax`

## Internationalization (i18n)

### 5. Text Domain Updates
- **React/JSX files**: Use `'reviewkit'` as text domain in all translation functions
- **PHP files**: Use `'reviewkit'` as text domain in all translation functions (`__()`, `_e()`, `_n()`, etc.)

## WordPress Hooks and Actions

### 6. Hook Prefixes
- Change all WordPress action/filter hooks from `trustpilot_reviewkit_` to `reviewkit_`
- Example: `wp_ajax_trustpilot_reviewkit_get_reviews` → `wp_ajax_reviewkit_get_reviews`

### 7. Security Nonces
- Change all nonce names from `trustpilot_reviewkit_` to `reviewkit_`
- Example: `trustpilot_reviewkit_nonce` → `reviewkit_nonce`
- Update nonce references in both PHP and React/JSX files

## Autoloading System

### 8. Replace Composer Autoloader
- Remove the composer-based autoload system completely
- Implement SPL autoload system instead
- **Critical**: Ensure existing code functionality remains intact during this transition

## Exclusions
- **Ignore**: Do not modify anything in the 'Reviews-kit' folder
- **Guidelines**: Strictly follow WordPress.org plugin guidelines throughout all changes

## Validation Requirements
- Test all functionality after each major change
- Ensure no existing features are broken
- Verify all renamed files and classes are properly referenced
- Confirm all hooks and nonces work correctly with new naming