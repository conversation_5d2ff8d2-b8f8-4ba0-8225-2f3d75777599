---
type: "manual"
---

- use simple ui, to make react components
- use scss
- use wp_options for storing the data in database
- use wpdb global
- keep the classes loosly coupled
- make smaller components in react.js
- don't break the existing code. always read the code first. try to understand the behaviour of the code.


the following json is the output of the api - 'https://api.gutensuite.net/data/tp/v1/reviews/elegantthemes.com'
in the api , the part "elegantthemes.com" is the business profile. this will be change for others business.

{
  "total_pages": 1234,
  "next_page_url": "https://www.trustpilot.com/review/elegantthemes.com?page=2",
  "total_reviews": 24671,
  "total_fetchable_pages": 30,
  "average_rating": 4.9,
  "business_details": {
    "id": "583562180000ff000597f458",
    "displayName": "Elegant Themes",
    "identifyingName": "www.elegantthemes.com",
    "numberOfReviews": 24671,
    "trustScore": 4.9,
    "websiteUrl": "http://www.elegantthemes.com",
    "websiteTitle": "www.elegantthemes.com",
    "profileImageUrl": "//s3-eu-west-1.amazonaws.com/tpd/logos/583562180000ff000597f458/0x0.png",
    "customHeaderUrl": "",
    "promotion": null,
    "hideCompetitorModule": false,
    "stars": 5,
    "categories": [
      {
        "id": "software_company",
        "name": "Software Company",
        "rank": "undefined",
        "cardinality": "undefined",
        "isPrimary": true
      }
    ],
    "breadcrumb": {
      "topLevelId": "electronics_technology",
      "topLevelDisplayName": "Electronics & Technology",
      "midLevelId": "internet_software",
      "midLevelDisplayName": "Internet & Software",
      "bottomLevelId": "software_company",
      "bottomLevelDisplayName": "Software Company"
    },
    "isClaimed": true,
    "isClosed": false,
    "isTemporarilyClosed": false,
    "locationsCount": 0,
    "isCollectingReviews": false,
    "verification": {
      "verifiedByGoogle": false,
      "verifiedPaymentMethod": true,
      "verifiedUserIdentity": true
    },
    "hasCollectedIncentivisedReviews": false,
    "consumerAlert": null,
    "consumerAlerts": [],
    "isMerged": false,
    "contactInfo": {
      "email": "<EMAIL>",
      "address": "977 West Napa Street #1002",
      "city": "Sonoma",
      "country": "US",
      "phone": "",
      "zipCode": "95476"
    },
    "activity": {
      "isUsingPaidFeatures": true,
      "hasSubscription": true,
      "isAskingForReviews": false,
      "claimedDate": "2017-09-29T18:49:23.000Z",
      "isClaimed": true,
      "previouslyClaimed": true,
      "replyBehavior": {
        "averageDaysToReply": 2.12,
        "lastReplyToNegativeReview": "2025-07-21 18:12:28 UTC",
        "negativeReviewsWithRepliesCount": 25,
        "replyPercentage": 100,
        "totalNegativeReviewsCount": 25
      },
      "verification": {
        "verifiedByGoogle": false,
        "verifiedPaymentMethod": true,
        "verifiedUserIdentity": true
      },
      "hasBusinessUnitMergeHistory": false,
      "basiclinkRate": 91,
      "hideBasicLinkAlert": false,
      "isUsingAIResponses": false
    },
    "hasCustomHeaderSetting": true,
    "hasPromotionSetting": true
  },
  "reviews": {
    "687e37cdffe7b97487bb929e": {
      "reviewId": "687e37cdffe7b97487bb929e",
      "reviewUrl": "https://www.trustpilot.com/reviews//687e37cdffe7b97487bb929e",
      "rating": 1,
      "reviewTitle": "Slow and buggy",
      "reviewBody": "Slow and buggy. \n\nI generally stay away from builders as they always produce a bloated mess of a website.\n\nBut i've inherited a website using Divi so here we are. I can honestly say it's worst builder i've ever used. It's super slow and unresponsive to the point where it's barely usable in the builder. Doing simple tasks which would take seconds , using divi it will take minutes. Customer support is awful as well, giving very generic basic responses. Terrible product, honestly I don't understand how its 4.9 stars. \n\nGo and use bricks if your going use a builder. ",
      "customer": {
        "id": "684ace648a5375aaaa68d9e9",
        "name": "Aidan",
        "image": ""
      },
      "dates": {
        "experiencedDate": "2025-07-21T00:00:00.000Z",
        "publishedDate": "2025-07-21T14:51:25.000Z",
        "updatedDate": null,
        "submittedDate": null
      }
    },
    "687d58795d7b8da6a53e02c8": {
      "reviewId": "687d58795d7b8da6a53e02c8",
      "reviewUrl": "https://www.trustpilot.com/reviews//687d58795d7b8da6a53e02c8",
      "rating": 5,
      "reviewTitle": "Definitely deserve 5 stars for now!",
      "reviewBody": "Definitely deserve 5 stars for now!\nIt was a very nice experience to have a bug fixed right away. I was amazed how quick they were able to solve the problem directly on my live site.",
      "customer": {
        "id": "62fd1df5f22432001209e776",
        "name": "MIHAI",
        "image": ""
      },
      "dates": {
        "experiencedDate": "2025-07-20T00:00:00.000Z",
        "publishedDate": "2025-07-20T22:58:33.000Z",
        "updatedDate": null,
        "submittedDate": null
      }
    },
    "687cb02677b1ba8f32c1a649": {
      "reviewId": "687cb02677b1ba8f32c1a649",
      "reviewUrl": "https://www.trustpilot.com/reviews//687cb02677b1ba8f32c1a649",
      "rating": 5,
      "reviewTitle": "Thank you very much for your support.",
      "reviewBody": "Thank you very much for your support.",
      "customer": {
        "id": "687cb020b81b2ce6efdc5079",
        "name": "Mehmet YÜKSEL",
        "image": "https://user-images.trustpilot.com/687cb020b81b2ce6efdc5079/73x73.png"
      },
      "dates": {
        "experiencedDate": "2025-07-20T00:00:00.000Z",
        "publishedDate": "2025-07-20T11:00:22.000Z",
        "updatedDate": null,
        "submittedDate": null
      }
    },
    "687b8f361307602172d39cb7": {
      "reviewId": "687b8f361307602172d39cb7",
      "reviewUrl": "https://www.trustpilot.com/reviews//687b8f361307602172d39cb7",
      "rating": 5,
      "reviewTitle": "Amazing support ",
      "reviewBody": "Amazing support ! SO to Manoj !",
      "customer": {
        "id": "687b8f31e3b0fa0f3be6ff93",
        "name": "Simon",
        "image": "https://user-images.trustpilot.com/687b8f31e3b0fa0f3be6ff93/73x73.png"
      },
      "dates": {
        "experiencedDate": "2025-07-19T00:00:00.000Z",
        "publishedDate": "2025-07-19T14:27:34.000Z",
        "updatedDate": null,
        "submittedDate": null
      }
    },
    "68797019bf78ae62d92b9eff": {
      "reviewId": "68797019bf78ae62d92b9eff",
      "reviewUrl": "https://www.trustpilot.com/reviews//68797019bf78ae62d92b9eff",
      "rating": 5,
      "reviewTitle": "Divi support is simply the best.",
      "reviewBody": "Divi support is simply the best. ",
      "customer": {
        "id": "5df3eb2f2ed7ce1ee8fbe0cc",
        "name": "Customer",
        "image": ""
      },
      "dates": {
        "experiencedDate": "2025-07-17T00:00:00.000Z",
        "publishedDate": "2025-07-17T23:50:17.000Z",
        "updatedDate": null,
        "submittedDate": null
      }
    },
    "68789739d5432c5f8f219f70": {
      "reviewId": "68789739d5432c5f8f219f70",
      "reviewUrl": "https://www.trustpilot.com/reviews//68789739d5432c5f8f219f70",
      "rating": 5,
      "reviewTitle": "Support services are briiliant.",
      "reviewBody": "The support services with Elegant Themes is second to none. No question is too big or too small (Ive sent through little niggly design questions) for the team. I usually receive a straight forward answer, with screenshots to aid the instructions, super quickly. Always impressed. Thanks team!! :) ",
      "customer": {
        "id": "556793380000ff0001c22e70",
        "name": "Melissa Clarke",
        "image": ""
      },
      "dates": {
        "experiencedDate": "2025-07-17T00:00:00.000Z",
        "publishedDate": "2025-07-17T08:24:57.000Z",
        "updatedDate": null,
        "submittedDate": null
      }
    }
  },
  "filters": {
    "totalNumberOfReviews": 23047,
    "totalNumberOfFilteredReviews": 21731,
    "pagination": {
      "currentPage": 1,
      "perPage": 20,
      "totalCount": 21731,
      "totalPages": 1087
    },
    "selected": {
      "languages": "en",
      "date": null,
      "stars": null,
      "aspects": null,
      "topics": null,
      "search": null,
      "locationId": null,
      "sort": "recency",
      "verified": false,
      "replies": false
    }
  },
  "error": null
}
 