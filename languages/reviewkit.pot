# Copyright (C) 2025 GutenSuite
# This file is distributed under the GPL-2.0-or-later.
msgid ""
msgstr ""
"Project-Id-Version: Reviewkit 1.0.0\n"
"Report-Msgid-Bugs-To: https://wordpress.org/support/plugin/reviewkit\n"
"Last-Translator: FULL NAME <EMAIL@ADDRESS>\n"
"Language-Team: LANGUAGE <<EMAIL>>\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"POT-Creation-Date: 2025-08-18T16:58:17+00:00\n"
"PO-Revision-Date: YEAR-MO-DA HO:MI+ZONE\n"
"X-Generator: WP-CLI 2.11.0\n"
"X-Domain: reviewkit\n"

#. Plugin Name of the plugin
#: reviewkit.php
#: includes/Admin/class-menu.php:22
#: includes/Admin/class-menu.php:23
msgid "Reviewkit"
msgstr ""

#. Description of the plugin
#: reviewkit.php
msgid "Automatically sync and display your business reviews on your WordPress site with customizable widgets, trust badges, and real-time updates. Boost credibility and conversions by showcasing genuine customer testimonials anywhere on your site with easy shortcodes and flexible layouts."
msgstr ""

#. Author of the plugin
#: reviewkit.php
msgid "GutenSuite"
msgstr ""

#: react_app/components/pages/reviews/index.jsx:28
#: assets/js/reviewkit.core.min.js:279
msgid "Business URL"
msgstr ""

#: react_app/components/pages/reviews/index.jsx:30
#: assets/js/reviewkit.core.min.js:281
msgid "Enter the URL of your business page"
msgstr ""

#: react_app/components/pages/reviews/index.jsx:42
#: assets/js/reviewkit.core.min.js:293
msgid "Fetch Reviews"
msgstr ""

#: react_app/components/pages/reviews/index.jsx:49
#: assets/js/reviewkit.core.min.js:300
msgid "Sync Reviews"
msgstr ""
