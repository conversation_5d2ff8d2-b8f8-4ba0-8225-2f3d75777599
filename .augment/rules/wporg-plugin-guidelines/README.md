# WordPress Plugin Directory Guidelines

This is the online repository for the rewrite of the WordPress.org Plugin Guidelines.

As of January 11, 2018, these guidelines match the ones on WordPress.org.

* [Changelog](https://github.com/wordpress/wporg-plugin-guidelines/blob/master/CHANGELOG.md)
* [Open Issues](https://github.com/wordpress/wporg-plugin-guidelines)

## Introduction

In 2016, an attempt was begun to clean up the [Detailed Plugin Guidelines](https://developer.wordpress.org/plugins/wordpress-org/detailed-plugin-guidelines/) and make them easier to understand, while retaining the heart of their intent. While one might wish we could say "Don't be a gosh darn bad person or spammer!" the reality is that some people need things spelled out. But also a great many situations are difficult to summarize.

The Plugin Review Team made the first pass, followed by many volunteers at WordCamps around the world, and finally a public posting here for anyone in the community.

Since then, we have used this repository to track changes and discuss major issues and amendments to the guidelines.

## Feedback

If you feel a guideline’s explanation is unclear, please create an issue or a pull request with what you feel should be changed and why. All grammar/spelling corrections are greatly welcome. We’re trying to write these for all levels of developers, as well as people who may not speak English proficiently. Using words like ‘obsequious’ should be avoided (nb: That’s mostly to Mika who uses those words regularly).

## Table of Contents

[Introduction](https://github.com/wordpress/wporg-plugin-guidelines/blob/master/introduction.md)

1. [Plugins must be compatible with the GNU General Public License v2](https://github.com/wordpress/wporg-plugin-guidelines/blob/master/guideline-01.md)
2. [Developers are responsible for the contents and actions of their plugins.](https://github.com/wordpress/wporg-plugin-guidelines/blob/master/guideline-02.md)
3. [A stable version of a plugin must be available from its WordPress Plugin Directory page.](https://github.com/wordpress/wporg-plugin-guidelines/blob/master/guideline-03.md)
4. [Code must be (mostly) human readable.](https://github.com/wordpress/wporg-plugin-guidelines/blob/master/guideline-04.md)
5. [Trialware is not permitted.](https://github.com/wordpress/wporg-plugin-guidelines/blob/master/guideline-05.md)
6. [Software as a Service is permitted.](https://github.com/wordpress/wporg-plugin-guidelines/blob/master/guideline-06.md)
7. [Plugins may not track users without their consent.](https://github.com/wordpress/wporg-plugin-guidelines/blob/master/guideline-07.md)
8. [Plugins may not send executable code via third-party systems.](https://github.com/wordpress/wporg-plugin-guidelines/blob/master/guideline-08.md)
9. [Developers and their plugins must not do anything illegal, dishonest, or morally offensive.](https://github.com/wordpress/wporg-plugin-guidelines/blob/master/guideline-09.md)
10. [Plugins may not embed external links or credits on the public site without explicitly asking the user’s permission.](https://github.com/wordpress/wporg-plugin-guidelines/blob/master/guideline-10.md)
11. [Plugins should not hijack the admin dashboard.](https://github.com/wordpress/wporg-plugin-guidelines/blob/master/guideline-11.md)
12. [Public facing pages on WordPress.org (readmes) must not spam.](https://github.com/wordpress/wporg-plugin-guidelines/blob/master/guideline-12.md)
13. [Plugins must use WordPress’ default libraries.](https://github.com/wordpress/wporg-plugin-guidelines/blob/master/guideline-13.md)
14. [Frequent commits to a plugin should be avoided.](https://github.com/wordpress/wporg-plugin-guidelines/blob/master/guideline-14.md)
15. [Plugin version numbers must be incremented for each new release.](https://github.com/wordpress/wporg-plugin-guidelines/blob/master/guideline-15.md)
16. [A complete plugin must be available at the time of submission.](https://github.com/wordpress/wporg-plugin-guidelines/blob/master/guideline-16.md)
17. [Plugins must respect trademarks, copyrights, and project names.](https://github.com/wordpress/wporg-plugin-guidelines/blob/master/guideline-17.md)
18. [We reserve the right to maintain the Plugin Directory to the best of our ability.](https://github.com/wordpress/wporg-plugin-guidelines/blob/master/guideline-18.md)

## License

The content has two licenses:

- [GPLv2](https://github.com/wordpress/wporg-plugin-guidelines/LICENSE)
- [Creative Commons Sharealike](https://creativecommons.org/licenses/by-sa/4.0/)

Just like WordPress, you are free to read, share, distribute, and modify the content however you want, passing on those freedoms to everyone else. Cool!
