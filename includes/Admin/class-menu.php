<?php

if ( ! defined( 'ABSPATH' ) ) {
	exit; // Exit if accessed directly
}

class Reviewkit_Admin_Menu {
	/**
	 * add plugin menu page and submenu pages
	 */
	public function __construct() {
		add_action( 'admin_menu', array( $this, 'admin_menu' ) );
	}

	/**
	 * add admin menu page
	 *
	 * @return hooks
	 */
	public function admin_menu() {
		add_menu_page(
			__( 'Reviewkit', 'reviewkit' ),
			__( 'Reviewkit', 'reviewkit' ),
			'manage_options',
			'gutensuite/reviewkit',
			array( $this, 'load_main_template' ),
			'dashicons-star-half',
			6
		);
	}

	/**
	 * dashboard root
	 *
	 * @return hooks
	 */
	public function load_main_template() {
		echo '<div id="reviewkit-body" class="reviewkit-body"></div>';
	}
}
