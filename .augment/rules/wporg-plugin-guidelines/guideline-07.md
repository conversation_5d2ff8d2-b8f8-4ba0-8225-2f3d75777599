<h4>7. Plugins may not track users without their consent.</h4>

In the interest of protecting user privacy, plugins may not contact external servers without <em>explicit</em> and authorized consent. This is commonly done via an 'opt in' method, requiring registration with a service or a checkbox within the plugin settings. Documentation on how any user data is collected, and used, should be included in the plugin’s readme, preferably with a clearly stated privacy policy.

Some examples of prohibited tracking include:

<ul>
	<li>Automated collection of user data without explicit confirmation from the user.</li>
	<li>Intentionally misleading users into submitting information as a requirement for use of the plugin itself.</li>
	<li>Offloading assets (including images and scripts) that are unrelated to a service.</li>
	<li>Undocumented (or poorly documented) use of external data (such as blocklists).</li>
	<li>Third-party advertisement mechanisms which track usage and/or views.</li>
</ul>

An exception to this policy is Software as a Service, such as Twitter, an Amazon CDN plugin, or Akismet. By installing, activating, registering, and configuring plugins that utilize those services, consent is granted for those systems.