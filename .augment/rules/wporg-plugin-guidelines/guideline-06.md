<h4>6. Software as a Service is permitted.</h4>

Plugins that act as an interface to some external third party service (e.g. a video hosting site) are allowed, even for paid services. The service itself must provide functionality of substance and be clearly documented in the readme file submitted with the plugin, preferably with a link to the service’s Terms of Use.

Services and functionality <em>not</em> allowed include:

<ul>
	<li>A service that exists for the sole purpose of validating licenses or keys while all functional aspects of the plugin are included locally is not permitted.</li>
	<li>Creation of a service by moving arbitrary code out of the plugin so that the service may falsely appear to provide supplemented functionality is prohibited.</li>
	<li>Storefronts that are not services. A plugin that acts only as a front-end for products to be purchased from external systems will not be accepted.</li>
</ul>