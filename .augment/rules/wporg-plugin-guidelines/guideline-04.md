<h4>4. Code must be (mostly) human readable.</h4>

Obscuring code by hiding it with techniques or systems similar to <code>p,a,c,k,e,r</code>'s obfuscate feature, uglify's mangle, or unclear naming conventions such as <code>$z12sdf813d</code>, is not permitted in the directory. Making code non-human readable forces future developers to face an unnecessary hurdle, as well as being a common vector for hidden, malicious code.

We require developers to provide public, maintained access to their source code and any build tools in one of the following ways:

<ul>
    <li>Include the source code in the deployed plugin</li>
    <li>A link in the readme to the development location</li>
</ul>

We strongly recommend you document how any development tools are to be used.
